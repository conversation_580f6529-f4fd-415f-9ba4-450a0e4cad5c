import request from "./request";
import { ReportData } from "@/types/api";

export const addRepairOrderApi = (data: {
    title: string;
    description: string;
    images: string[];
    category: string;
    status: string;
    priority: string;
    reporter: number;
    handlerId: number;
    cost: number;
    completionImages: string[];
}): Promise<ReportData<any>> => {
    return request.post('/repairOrder/add', data)
}

export const getRepairOrderListApi = (data: {
    pageNum: number;
    pageSize: number;
    searchKey: string;
    userId?: number;
    title?: string;
    description?: string;
    category?: string;
    status?: string;
    priority?: string;
    reporter?: string;
    handlerId?: number;
    cost?: number;
    completionImages?: string[];
}): Promise<ReportData<any>> => {
    return request.post('/repairOrder/pageList', data)
}

export const getAllRepairOrderListApi = (data: {
    searchKey: string;
    userId?: number;
    title?: string;
    description?: string;
    category?: string;
    status?: string;
    priority?: string;
    reporter?: string;
    handlerId?: number;
    cost?: number;
    completionImages?: string[];
}): Promise<ReportData<any>> => {
    return request.post('/repairOrder/list', data)
}

export const editRepairOrderApi = (data: {
    repairOrderId: number;
    title: string;
    description: string;
    images: string[];
    category: string;
    status: string;
    priority: string;
    reporter: string;
    handlerId: number;
    cost: number;
    completionImages: string[];
}): Promise<ReportData<any>> => {
    return request.put('/repairOrder/edit', data)
}

export const deleteRepairOrderApi = (repairOrderId: number): Promise<ReportData<any>> => {
    return request.delete(`/repairOrder/delete/${repairOrderId}`)
}

export const getRepairOrderByIdApi = (repairOrderId: number): Promise<ReportData<any>> => {
    return request.get(`/repairOrder/getRepairOrderById/${repairOrderId}`)
}