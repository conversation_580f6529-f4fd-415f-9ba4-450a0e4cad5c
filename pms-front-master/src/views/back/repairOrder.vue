<script setup>
import { ref, reactive, watch, onMounted } from 'vue';
import { Search, Plus, Edit, Delete, Phone, Message, Location, User, Calendar, Money, View, Loading, Tools, Setting } from '@element-plus/icons-vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import BackHeader from '@/components/BackHeader.vue';
import { getAllUserListApi } from '@/request/userApi';
import { getAllHouseListApi } from '@/request/houseApi';
import { addRepairOrderApi, editRepairOrderApi, deleteRepairOrderApi, getRepairOrderByIdApi, getRepairOrderListApi } from '@/request/repairOrderApi';

// 搜索关键词
const searchKeyword = ref('');

// 当前页码
const currentPage = ref(1);
// 每页显示条数
const pageSize = ref(10);
// 总条数
const total = ref(0);
// 筛选条件
const filterValue = ref('全部状态');

// 工单数据
const workOrders = reactive([]);
const loadingWorkOrders = ref(false);

// 用户选项数据
const userOptions = reactive([]);
const loadingUsers = ref(false);

// 房产选项数据
const houseOptions = reactive([]);
const loadingHouses = ref(false);

// 添加工单对话框相关
const addDialogVisible = ref(false);
const addFormRef = ref(null);
const isSubmitting = ref(false);

// 查看工单详情对话框相关
const viewDialogVisible = ref(false);
const viewWorkOrderData = reactive({});

// 编辑工单对话框相关
const editDialogVisible = ref(false);
const editFormRef = ref(null);
const isEditSubmitting = ref(false);
const editWorkOrderData = reactive({
  repairOrderId: '',
  userId: '',
  title: '',
  description: '',
  images: [],
  category: '',
  status: '',
  priority: '',
  reporter: '',
  assignee: '',
  cost: '',
  completionImages: []
});

// 新工单表单数据
const newWorkOrder = reactive({
  userId: '',
  title: '',
  description: '',
  images: [],
  category: '',
  status: '',
  priority: '',
  reporter: '',
  assignee: '',
  cost: '',
  completionImages: []
});

// 表单验证规则
const rules = {
  userId: [
    { required: true, message: '请选择用户', trigger: 'change' }
  ],
  title: [
    { required: true, message: '请输入工单标题', trigger: 'blur' },
    { min: 2, max: 50, message: '长度在 2 到 50 个字符', trigger: 'blur' }
  ],
  description: [
    { required: true, message: '请输入问题描述', trigger: 'blur' },
    { min: 5, max: 500, message: '长度在 5 到 500 个字符', trigger: 'blur' }
  ],
  category: [
    { required: true, message: '请选择问题类别', trigger: 'change' }
  ],
  status: [
    { required: true, message: '请选择工单状态', trigger: 'change' }
  ],
  priority: [
    { required: true, message: '请选择优先级', trigger: 'change' }
  ],
  reporter: [
    { required: true, message: '请输入报修人', trigger: 'blur' }
  ],
  assignee: [
    { required: true, message: '请输入处理人', trigger: 'blur' }
  ],
  cost: [
    { required: true, message: '请输入维修费用', trigger: 'blur' },
    { pattern: /^\d+(\.\d{1,2})?$/, message: '请输入正确的金额', trigger: 'blur' }
  ]
};

// 数据转换函数
const getStatusLabel = (status) => {
  const statusMap = {
    'pending': '待处理',
    'processing': '处理中',
    'reviewing': '待验收',
    'completed': '已完成'
  };
  return statusMap[status] || status;
};

const getStatusClass = (status) => {
  const classMap = {
    '待处理': 'status-pending',
    '处理中': 'status-processing',
    '待验收': 'status-reviewing',
    '已完成': 'status-completed'
  };
  return classMap[status] || '';
};

const getPriorityLabel = (priority) => {
  const priorityMap = {
    'low': '低优先级',
    'medium': '中优先级',
    'high': '高优先级',
    'urgent': '紧急'
  };
  return priorityMap[priority] || priority;
};

const getPriorityClass = (priority) => {
  const classMap = {
    '低优先级': 'priority-low',
    '中优先级': 'priority-medium',
    '高优先级': 'priority-high',
    '紧急': 'priority-urgent'
  };
  return classMap[priority] || '';
};

const getCategoryLabel = (category) => {
  const categoryMap = {
    'plumbing': '水电维修',
    'electrical': '电器维修',
    'hvac': '空调维修',
    'structural': '结构维修',
    'cleaning': '清洁服务',
    'other': '其他'
  };
  return categoryMap[category] || category;
};

// 格式化日期为 yyyy-MM-dd HH:mm:ss
const formatDate = (dateStr) => {
  if (!dateStr) return '';
  const date = new Date(dateStr);
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, '0');
  const day = String(date.getDate()).padStart(2, '0');
  const hours = String(date.getHours()).padStart(2, '0');
  const minutes = String(date.getMinutes()).padStart(2, '0');
  const seconds = String(date.getSeconds()).padStart(2, '0');
  return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
};

// 格式化日期为 yyyy-MM-dd（用于日期选择器）
const formatDateOnly = (dateStr) => {
  if (!dateStr) return '';
  const date = new Date(dateStr);
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, '0');
  const day = String(date.getDate()).padStart(2, '0');
  return `${year}-${month}-${day}`;
};

// 将日期字符串转换为 yyyy-MM-dd HH:mm:ss 格式
const formatDateTimeForAPI = (dateStr) => {
  if (!dateStr) return '';
  // 如果只有日期，添加默认时间
  if (dateStr.length === 10) {
    return `${dateStr} 00:00:00`;
  }
  return dateStr;
};

// 处理用户选择变化，自动填入用户信息
const handleUserChange = (userId) => {
  if (!userId) {
    // 如果清空用户选择，也清空相关字段
    newWorkOrder.reporter = '';
    return;
  }

  // 查找选中的用户信息
  const selectedUser = userOptions.find(option => option.value === userId);
  if (selectedUser && selectedUser.userData) {
    const userData = selectedUser.userData;
    // 自动填入用户信息
    newWorkOrder.reporter = userData.nickname || userData.username || '';
  }
};

// 处理编辑时用户选择变化
const handleEditUserChange = (userId) => {
  if (!userId) {
    return; // 编辑时不清空其他字段
  }

  // 查找选中的用户信息
  const selectedUser = userOptions.find(option => option.value === userId);
  if (selectedUser && selectedUser.userData) {
    const userData = selectedUser.userData;
    // 自动填入用户信息
    editWorkOrderData.reporter = userData.nickname || userData.username || '';
  }
};

// 处理搜索
const handleSearch = () => {
  currentPage.value = 1;
  loadWorkOrderList();
};

// 处理筛选
const handleFilter = (filterItem) => {
  filterValue.value = filterItem;
  currentPage.value = 1;
  loadWorkOrderList();
};

// 获取筛选状态对应的值
const getFilterStatusValue = (filterLabel) => {
  const statusMap = {
    '待处理': 'pending',
    '处理中': 'processing',
    '待验收': 'reviewing',
    '已完成': 'completed'
  };
  return statusMap[filterLabel];
};

// 获取用户列表
const loadUserOptions = async () => {
  try {
    loadingUsers.value = true;
    const response = await getAllUserListApi({});

    if (response.code === 200 && response.data) {
      userOptions.length = 0;
      response.data.forEach(user => {
        userOptions.push({
          value: user.userId,
          label: user.nickname || user.username,
          // 保存完整的用户信息用于自动填入
          userData: {
            userId: user.userId,
            username: user.username,
            nickname: user.nickname,
            email: user.email,
            phone: user.phone,
            sex: user.sex
          }
        });
      });
    } else {
      ElMessage.error('获取用户列表失败');
    }
  } catch (error) {
    console.error('获取用户列表失败:', error);
    ElMessage.error('获取用户列表失败，请稍后重试');
  } finally {
    loadingUsers.value = false;
  }
};

// 获取房产列表
const loadHouseOptions = async () => {
  try {
    loadingHouses.value = true;
    const response = await getAllHouseListApi({});

    if (response.code === 200 && response.data) {
      houseOptions.length = 0;
      response.data.forEach(house => {
        houseOptions.push({
          value: house.houseId || house.id,
          label: `${house.building} ${house.room}`
        });
      });
    } else {
      ElMessage.error('获取房产列表失败');
    }
  } catch (error) {
    console.error('获取房产列表失败:', error);
    ElMessage.error('获取房产列表失败，请稍后重试');
  } finally {
    loadingHouses.value = false;
  }
};

// 获取工单列表
const loadWorkOrderList = async () => {
  try {
    loadingWorkOrders.value = true;
    const response = await getRepairOrderListApi({
      pageNum: currentPage.value,
      pageSize: pageSize.value,
      searchKey: searchKeyword.value || undefined,
      status: filterValue.value === '全部状态' ? undefined : getFilterStatusValue(filterValue.value)
    });
    if (response.code === 200 && response.data) {
      workOrders.length = 0;
      response.data.forEach(workOrder => {
        workOrders.push({
          id: workOrder.repairOrderId || workOrder.id,
          repairOrderId: workOrder.repairOrderId || workOrder.id,
          userId: workOrder.userId,
          title: workOrder.title,
          description: workOrder.description,
          images: workOrder.images || [],
          category: getCategoryLabel(workOrder.category),
          status: getStatusLabel(workOrder.status),
          priority: getPriorityLabel(workOrder.priority),
          reporter: workOrder.reporter,
          assignee: workOrder.assignee,
          cost: `¥${workOrder.cost}`,
          completionImages: workOrder.completionImages || [],
          createTime: formatDate(workOrder.createTime),
          updateTime: formatDate(workOrder.updateTime)
        });
      });

      total.value = response.total || workOrders.length;
    } else {
      ElMessage.error('获取工单列表失败');
    }
  } catch (error) {
    console.error('获取工单列表失败:', error);
    ElMessage.error('获取工单列表失败，请稍后重试');
  } finally {
    loadingWorkOrders.value = false;
  }
};

// 锁定页面滚动
const lockBodyScroll = () => {
  const scrollY = window.scrollY;
  document.documentElement.classList.add('dialog-open');
  document.body.classList.add('dialog-open');
  document.body.style.overflow = 'hidden';
  document.body.style.position = 'fixed';
  document.body.style.top = `-${scrollY}px`;
  document.body.style.left = '0';
  document.body.style.right = '0';
  document.body.style.width = '100%';
  document.body.style.height = '100%';
  document.body.dataset.scrollY = scrollY.toString();
};

// 解锁页面滚动
const unlockBodyScroll = () => {
  const scrollY = parseInt(document.body.dataset.scrollY || '0');
  document.documentElement.classList.remove('dialog-open');
  document.body.classList.remove('dialog-open');
  document.body.style.overflow = '';
  document.body.style.position = '';
  document.body.style.top = '';
  document.body.style.left = '';
  document.body.style.right = '';
  document.body.style.width = '';
  document.body.style.height = '';
  delete document.body.dataset.scrollY;
  window.scrollTo(0, scrollY);
};

// 添加新工单
const addNewWorkOrder = async () => {
  addDialogVisible.value = true;
  lockBodyScroll();

  // 加载选项数据
  await Promise.all([loadUserOptions(), loadHouseOptions()]);

  // 重置表单
  if (addFormRef.value) {
    addFormRef.value.resetFields();
  }
  // 重置表单数据
  Object.assign(newWorkOrder, {
    userId: '',
    title: '',
    description: '',
    images: [],
    category: '',
    status: 'pending',
    priority: 'medium',
    reporter: '',
    assignee: '',
    cost: '',
    completionImages: []
  });
};

// 提交新工单表单
const submitNewWorkOrder = async () => {
  if (!addFormRef.value) return;

  await addFormRef.value.validate(async (valid) => {
    if (valid) {
      try {
        isSubmitting.value = true;

        const response = await addRepairOrderApi({
          userId: parseInt(newWorkOrder.userId),
          title: newWorkOrder.title,
          description: newWorkOrder.description,
          images: newWorkOrder.images,
          category: newWorkOrder.category,
          status: newWorkOrder.status,
          priority: newWorkOrder.priority,
          reporter: newWorkOrder.reporter,
          assignee: newWorkOrder.assignee,
          cost: parseFloat(newWorkOrder.cost),
          completionImages: newWorkOrder.completionImages
        });

        if (response.code === 200) {
          ElMessage.success('添加工单成功');
          addDialogVisible.value = false;
          unlockBodyScroll();
          await loadWorkOrderList();
        } else {
          ElMessage.error(response.msg || '添加工单失败');
        }
      } catch (error) {
        console.error('添加工单失败:', error);
        ElMessage.error('添加工单失败，请稍后重试');
      } finally {
        isSubmitting.value = false;
      }
    }
  });
};

// 取消添加
const cancelAdd = () => {
  addDialogVisible.value = false;
  unlockBodyScroll();
};

// 查看工单详情
const viewWorkOrderDetail = async (workOrder) => {
  try {
    const response = await getRepairOrderByIdApi(workOrder.repairOrderId || workOrder.id);
    if (response.code === 200 && response.data) {
      Object.assign(viewWorkOrderData, {
        ...response.data,
        statusLabel: getStatusLabel(response.data.status),
        priorityLabel: getPriorityLabel(response.data.priority),
        categoryLabel: getCategoryLabel(response.data.category),
        createTimeFormatted: formatDate(response.data.createTime),
        updateTimeFormatted: formatDate(response.data.updateTime)
      });
      viewDialogVisible.value = true;
      lockBodyScroll();
    } else {
      ElMessage.error('获取工单详情失败');
    }
  } catch (error) {
    console.error('获取工单详情失败:', error);
    ElMessage.error('获取工单详情失败，请稍后重试');
  }
};

// 关闭查看详情对话框
const closeViewDialog = () => {
  viewDialogVisible.value = false;
  unlockBodyScroll();
};

// 编辑工单
const editWorkOrder = async (workOrder) => {
  try {
    // 先加载选项数据
    await Promise.all([loadUserOptions(), loadHouseOptions()]);

    const response = await getRepairOrderByIdApi(workOrder.repairOrderId || workOrder.id);
    if (response.code === 200 && response.data) {
      Object.assign(editWorkOrderData, {
        repairOrderId: response.data.repairOrderId || response.data.id,
        userId: response.data.userId,
        title: response.data.title,
        description: response.data.description,
        images: response.data.images || [],
        category: response.data.category,
        status: response.data.status,
        priority: response.data.priority,
        reporter: response.data.reporter,
        assignee: response.data.assignee,
        cost: response.data.cost,
        completionImages: response.data.completionImages || []
      });

      editDialogVisible.value = true;
      lockBodyScroll();

      // 重置表单验证状态
      if (editFormRef.value) {
        editFormRef.value.clearValidate();
      }
    } else {
      ElMessage.error('获取工单信息失败');
    }
  } catch (error) {
    console.error('获取工单信息失败:', error);
    ElMessage.error('获取工单信息失败，请稍后重试');
  }
};

// 提交编辑工单表单
const submitEditWorkOrder = async () => {
  if (!editFormRef.value) return;

  await editFormRef.value.validate(async (valid) => {
    if (valid) {
      try {
        isEditSubmitting.value = true;

        const response = await editRepairOrderApi({
          repairOrderId: parseInt(editWorkOrderData.repairOrderId),
          userId: parseInt(editWorkOrderData.userId),
          title: editWorkOrderData.title,
          description: editWorkOrderData.description,
          images: editWorkOrderData.images,
          category: editWorkOrderData.category,
          status: editWorkOrderData.status,
          priority: editWorkOrderData.priority,
          reporter: editWorkOrderData.reporter,
          assignee: editWorkOrderData.assignee,
          cost: parseFloat(editWorkOrderData.cost),
          completionImages: editWorkOrderData.completionImages
        });

        if (response.code === 200) {
          ElMessage.success('编辑工单成功');
          editDialogVisible.value = false;
          unlockBodyScroll();
          await loadWorkOrderList();
        } else {
          ElMessage.error(response.msg || '编辑工单失败');
        }
      } catch (error) {
        console.error('编辑工单失败:', error);
        ElMessage.error('编辑工单失败，请稍后重试');
      } finally {
        isEditSubmitting.value = false;
      }
    }
  });
};

// 取消编辑
const cancelEdit = () => {
  editDialogVisible.value = false;
  unlockBodyScroll();
};

// 删除工单
const deleteWorkOrder = async (workOrder) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除工单 "${workOrder.title}" 吗？此操作不可撤销。`,
      '删除确认',
      {
        confirmButtonText: '确定删除',
        cancelButtonText: '取消',
        type: 'warning',
        confirmButtonClass: 'el-button--danger'
      }
    );

    const response = await deleteRepairOrderApi(workOrder.repairOrderId || workOrder.id);
    if (response.code === 200) {
      ElMessage.success('删除工单成功');
      await loadWorkOrderList();
    } else {
      ElMessage.error(response.msg || '删除工单失败');
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除工单失败:', error);
      ElMessage.error('删除工单失败，请稍后重试');
    }
  }
};

// 处理页码变化
const handleCurrentChange = (val) => {
  currentPage.value = val;
  loadWorkOrderList();
};

// 监听对话框关闭事件
watch([addDialogVisible, editDialogVisible, viewDialogVisible], ([add, edit, view]) => {
  if (!add && !edit && !view) {
    // 移除滚动事件监听器
    window.removeEventListener('scroll', preventScroll);
    unlockBodyScroll();
  }
});

// 阻止滚动事件
const preventScroll = (e) => {
  e.preventDefault();
  e.stopPropagation();
  return false;
};

// 组件挂载时加载数据
onMounted(() => {
  loadWorkOrderList();
});
</script>

<template>
  <BackHeader activeMenu="workorders">
    <div class="workorders-container">
      <!-- 页面标题和添加按钮 -->
      <div class="page-header">
        <div class="title-section">
          <h1 class="page-title">工单管理</h1>
          <p class="page-subtitle">管理住户报修和维修服务请求</p>
        </div>

        <el-button type="primary" class="add-workorder-btn" @click="addNewWorkOrder">
          <el-icon><Plus /></el-icon>
          创建工单
        </el-button>
      </div>

      <!-- 添加工单对话框 -->
      <el-dialog
        v-model="addDialogVisible"
        title="创建新工单"
        width="800px"
        :close-on-click-modal="false"
        :modal="true"
        :lock-scroll="true"
        :center="true"
        :destroy-on-close="true"
        :top="'5vh'"
        class="add-workorder-dialog"
      >
        <div class="dialog-content">
          <el-form
            ref="addFormRef"
            :model="newWorkOrder"
            :rules="rules"
            label-width="100px"
            label-position="right"
            class="workorder-form"
          >
            <!-- 基本信息 -->
            <div class="form-section">
              <h3 class="section-title">
                <el-icon><User /></el-icon>
                基本信息
              </h3>

              <el-form-item label="用户" prop="userId">
                <el-select
                  v-model="newWorkOrder.userId"
                  placeholder="请选择用户"
                  style="width: 100%"
                  clearable
                  :loading="loadingUsers"
                  loading-text="加载用户列表中..."
                  no-data-text="暂无用户数据"
                  @change="handleUserChange"
                >
                  <el-option
                    v-for="user in userOptions"
                    :key="user.value"
                    :label="user.label"
                    :value="user.value"
                  />
                </el-select>
              </el-form-item>

              <el-form-item label="工单标题" prop="title">
                <el-input
                  v-model="newWorkOrder.title"
                  placeholder="请输入工单标题"
                />
              </el-form-item>

              <el-form-item label="问题描述" prop="description">
                <el-input
                  v-model="newWorkOrder.description"
                  type="textarea"
                  :rows="4"
                  placeholder="请详细描述问题"
                />
              </el-form-item>
            </div>

            <!-- 分类信息 -->
            <div class="form-section">
              <h3 class="section-title">
                <el-icon><Tools /></el-icon>
                分类信息
              </h3>

              <el-form-item label="问题类别" prop="category">
                <el-select
                  v-model="newWorkOrder.category"
                  placeholder="请选择问题类别"
                  style="width: 100%"
                >
                  <el-option label="水电维修" value="plumbing" />
                  <el-option label="电器维修" value="electrical" />
                  <el-option label="空调维修" value="hvac" />
                  <el-option label="结构维修" value="structural" />
                  <el-option label="清洁服务" value="cleaning" />
                  <el-option label="其他" value="other" />
                </el-select>
              </el-form-item>

              <el-form-item label="优先级" prop="priority">
                <el-select
                  v-model="newWorkOrder.priority"
                  placeholder="请选择优先级"
                  style="width: 100%"
                >
                  <el-option label="低优先级" value="low" />
                  <el-option label="中优先级" value="medium" />
                  <el-option label="高优先级" value="high" />
                  <el-option label="紧急" value="urgent" />
                </el-select>
              </el-form-item>

              <el-form-item label="工单状态" prop="status">
                <el-select
                  v-model="newWorkOrder.status"
                  placeholder="请选择工单状态"
                  style="width: 100%"
                >
                  <el-option label="待处理" value="pending" />
                  <el-option label="处理中" value="processing" />
                  <el-option label="待验收" value="reviewing" />
                  <el-option label="已完成" value="completed" />
                </el-select>
              </el-form-item>
            </div>

            <!-- 人员信息 -->
            <div class="form-section">
              <h3 class="section-title">
                <el-icon><User /></el-icon>
                人员信息
              </h3>

              <el-form-item label="报修人" prop="reporter">
                <el-input
                  v-model="newWorkOrder.reporter"
                  placeholder="请输入报修人姓名"
                  :readonly="!!newWorkOrder.userId"
                />
              </el-form-item>

              <el-form-item label="处理人" prop="assignee">
                <el-input
                  v-model="newWorkOrder.assignee"
                  placeholder="请输入处理人姓名"
                />
              </el-form-item>
            </div>

            <!-- 费用信息 -->
            <div class="form-section">
              <h3 class="section-title">
                <el-icon><Money /></el-icon>
                费用信息
              </h3>

              <el-form-item label="维修费用" prop="cost">
                <el-input
                  v-model="newWorkOrder.cost"
                  placeholder="请输入维修费用"
                >
                  <template #prepend>¥</template>
                </el-input>
              </el-form-item>
            </div>

          </el-form>
        </div>

        <template #footer>
          <div class="dialog-footer">
            <el-button @click="cancelAdd">取消</el-button>
            <el-button
              type="primary"
              @click="submitNewWorkOrder"
              :loading="isSubmitting"
            >
              {{ isSubmitting ? '创建中...' : '确认创建' }}
            </el-button>
          </div>
        </template>
      </el-dialog>

      <!-- 工单网格 -->
      <div class="workorders-grid">
        <div v-for="workOrder in workOrders" :key="workOrder.id" class="workorder-card">
          <div class="workorder-header">
            <div class="workorder-id">
              <span class="id-badge">{{ workOrder.id }}</span>
              <span class="workorder-title">{{ workOrder.title }}</span>
            </div>
            <el-button
                type="primary"
                text
                @click="viewWorkOrderDetail(workOrder)"
            >
              <el-icon><View /></el-icon>
            </el-button>
          </div>

          <div class="workorder-content">
            <div class="info-row">
              <div class="info-item">
                <div class="info-label">地点：</div>
                <div class="info-value">{{ workOrder.location }}</div>
              </div>
              <div class="info-item">
                <div class="info-label">房号：</div>
                <div class="info-value">{{ workOrder.location }}</div>
              </div>
            </div>

            <div class="info-row">
              <div class="info-item">
                <div class="info-label">报修人：</div>
                <div class="info-value">
                  <span v-if="workOrder.reporter.name !== '物业部门'">{{ workOrder.reporter.name }}</span>
                  <span v-else class="department-tag">{{ workOrder.reporter.name }}</span>
                  <span v-if="workOrder.reporter.phone" class="reporter-phone">
                    <el-icon><el-icon-phone /></el-icon>
                    {{ workOrder.reporter.phone }}
                  </span>
                </div>
              </div>
              <div class="info-item">
                <div class="info-label">优先级：</div>
                <div class="info-value">
                  <span class="priority-tag" :class="workOrder.priorityClass">
                    {{ workOrder.priority }}
                  </span>
                </div>
              </div>
            </div>

            <div class="info-row">
              <div class="info-item">
                <div class="info-label">维修费用：</div>
                <div class="info-value cost">{{ workOrder.cost }}</div>
              </div>
            </div>

            <div class="description-row">
              <div class="description">{{ workOrder.description }}</div>
            </div>

            <div class="workorder-footer">
              <div class="create-time">
                <el-icon><el-icon-time /></el-icon>
                创建时间：{{ workOrder.createTime }}
              </div>
              <div class="action-buttons">
                <el-button type="primary" size="small" link>
                  查看详情
                </el-button>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 分页 -->
      <div class="pagination-container">
        <div class="total-info">共 {{ total }} 条记录</div>
        <el-pagination
            background
            layout="prev, pager, next"
            :total="total"
            :current-page="currentPage"
            @current-change="handleCurrentChange"
        />
      </div>
    </div>
  </BackHeader>
</template>

<style scoped>
.workorders-container {
  padding: 0 24px 24px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 32px;
}

.page-title {
  font-size: 28px;
  font-weight: 600;
  margin: 0;
  color: #333;
}

.page-subtitle {
  font-size: 16px;
  color: #909399;
  margin: 10px 0 0 0;
}

.add-workorder-btn {
  display: flex;
  align-items: center;
  gap: 10px;
  height: 48px;
  font-size: 16px;
  padding: 0 20px;
}

.status-cards {
  display: flex;
  gap: 24px;
  margin-bottom: 32px;
}

.status-card {
  flex: 1;
  background-color: #fff;
  border-radius: 12px;
  padding: 28px;
  display: flex;
  align-items: center;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
}

.status-icon {
  width: 64px;
  height: 64px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 20px;
}

.status-icon .el-icon {
  font-size: 28px;
  color: #409EFF;
}

.status-card-0 .status-icon .el-icon {
  color: #409EFF;
}

.status-card-1 .status-icon .el-icon {
  color: #E6A23C;
}

.status-card-2 .status-icon .el-icon {
  color: #F56C6C;
}

.status-card-3 .status-icon .el-icon {
  color: #67C23A;
}

.status-info {
  display: flex;
  flex-direction: column;
}

.status-count {
  font-size: 32px;
  font-weight: 600;
  color: #333;
  margin-bottom: 8px;
}

.status-name {
  font-size: 16px;
  color: #909399;
}

.search-filter-bar {
  display: flex;
  margin-bottom: 32px;
}

.search-box {
  flex: 1;
  margin-right: 20px;
}

.search-input {
  width: 100%;
}

.search-input :deep(.el-input__inner) {
  height: 52px;
  font-size: 16px;
  padding: 0 20px;
}

.filter-button {
  display: flex;
  align-items: center;
  gap: 10px;
  height: 52px;
  font-size: 16px;
  padding: 0 20px;
}

.workorders-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 24px;
  margin-bottom: 32px;
}

@media (max-width: 1200px) {
  .workorders-grid {
    grid-template-columns: 1fr;
  }
}

.workorder-card {
  background-color: #fff;
  border-radius: 12px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
  overflow: hidden;
  position: relative;
  display: flex;
  flex-direction: column;
}

.workorder-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 24px;
  border-bottom: 1px solid #f0f0f0;
}

.workorder-id {
  display: flex;
  align-items: center;
}

.id-badge {
  background-color: #f5f7fa;
  color: #909399;
  padding: 6px 12px;
  border-radius: 6px;
  font-size: 14px;
  margin-right: 16px;
}

.workorder-title {
  font-weight: 500;
  font-size: 18px;
}

.workorder-content {
  padding: 20px 24px;
  flex: 1;
  display: flex;
  flex-direction: column;
}

.info-row {
  display: flex;
  margin-bottom: 16px;
}

.info-item {
  display: flex;
  align-items: flex-start;
  flex: 1;
}

.info-label {
  color: #909399;
  font-size: 15px;
  min-width: 80px;
  text-align: right;
  margin-right: 12px;
}

.info-value {
  color: #606266;
  font-size: 15px;
  flex: 1;
}

.reporter-phone {
  color: #409EFF;
  font-size: 12px;
  margin-left: 8px;
  display: flex;
  align-items: center;
}

.reporter-phone .el-icon {
  margin-right: 4px;
  font-size: 12px;
}

.department-tag {
  color: #409EFF;
  font-weight: 500;
}

.priority-tag {
  display: inline-block;
  padding: 2px 8px;
  border-radius: 4px;
  font-size: 12px;
}

.high-priority {
  background-color: #fef0f0;
  color: #f56c6c;
}

.medium-priority {
  background-color: #fdf6ec;
  color: #e6a23c;
}

.cost {
  font-weight: 500;
}

.description-row {
  margin-bottom: 16px;
}

.description {
  white-space: normal;
  word-break: break-all;
  line-height: 1.5;
  color: #606266;
  font-size: 14px;
}

.workorder-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: auto;
  padding-top: 16px;
  border-top: 1px dashed #ebeef5;
}

.create-time {
  color: #909399;
  font-size: 12px;
  display: flex;
  align-items: center;
}

.create-time .el-icon {
  margin-right: 4px;
  font-size: 12px;
}

.action-buttons {
  display: flex;
  gap: 8px;
}

.status-tag {
  display: inline-block;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
}

.status-pending {
  background-color: #e6f7ff;
  color: #1890ff;
}

.status-processing {
  background-color: #fff7e6;
  color: #fa8c16;
}

.status-reviewing {
  background-color: #fff2f0;
  color: #ff4d4f;
}

.status-completed {
  background-color: #f6ffed;
  color: #52c41a;
}

.pagination-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 20px;
}

.total-info {
  font-size: 14px;
  color: #909399;
}
</style>