<script setup>
import { ref, reactive, watch, onMounted } from 'vue';
import { Search, Plus, Edit, Delete, Phone, Message, Location, User, Calendar, Money, View, Loading, Tools, Setting, ArrowDown } from '@element-plus/icons-vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import BackHeader from '@/components/BackHeader.vue';
import { getAllUserListApi } from '@/request/userApi';
import { getAllHouseListApi } from '@/request/houseApi';
import { addRepairOrderApi, editRepairOrderApi, deleteRepairOrderApi, getRepairOrderByIdApi, getRepairOrderListApi } from '@/request/repairOrderApi';

// 搜索关键词
const searchKeyword = ref('');

// 当前页码
const currentPage = ref(1);
// 每页显示条数
const pageSize = ref(10);
// 总条数
const total = ref(0);
// 筛选条件
const filterValue = ref('全部状态');

// 工单数据
const workOrders = reactive([]);
const loadingWorkOrders = ref(false);

// 用户选项数据
const userOptions = reactive([]);
const loadingUsers = ref(false);

// 房产选项数据
const houseOptions = reactive([]);
const loadingHouses = ref(false);

// 添加工单对话框相关
const addDialogVisible = ref(false);
const addFormRef = ref(null);
const isSubmitting = ref(false);

// 查看工单详情对话框相关
const viewDialogVisible = ref(false);
const viewWorkOrderData = reactive({});

// 编辑工单对话框相关
const editDialogVisible = ref(false);
const editFormRef = ref(null);
const isEditSubmitting = ref(false);
const editWorkOrderData = reactive({
  orderId: '',
  reporter: '',
  houseId: '',
  title: '',
  description: '',
  category: '0',
  status: '0',
  priority: '1',
  handlerId: '',
  cost: '',
});

// 新工单表单数据
const newWorkOrder = reactive({
  reporter: '',
  houseId: '',
  title: '',
  description: '',
  category: '0',
  status: '0',
  priority: '1',
  handlerId: '',
  cost: '',
});

// 表单验证规则
const rules = {
  reporter: [
    { required: true, message: '请选择报修人', trigger: 'change' }
  ],
  houseId: [
    { required: true, message: '请选择房产', trigger: 'change' }
  ],
  title: [
    { required: true, message: '请输入工单标题', trigger: 'blur' },
    { min: 2, max: 50, message: '长度在 2 到 50 个字符', trigger: 'blur' }
  ],
  description: [
    { required: true, message: '请输入问题描述', trigger: 'blur' },
    { min: 5, max: 500, message: '长度在 5 到 500 个字符', trigger: 'blur' }
  ],
  category: [
    { required: true, message: '请选择问题类别', trigger: 'change' }
  ],
  status: [
    { required: true, message: '请选择工单状态', trigger: 'change' }
  ],
  priority: [
    { required: true, message: '请选择优先级', trigger: 'change' }
  ],
  handlerId: [
    { required: true, message: '请选择处理人', trigger: 'change' }
  ],
  cost: [
    { required: true, message: '请输入维修费用', trigger: 'blur' },
    { pattern: /^\d+(\.\d{1,2})?$/, message: '请输入正确的金额', trigger: 'blur' }
  ]
};

// 数据转换函数
const getStatusLabel = (status) => {
  const statusMap = {
    '0': '待处理',
    '1': '处理中',
    '2': '已完成',
    '3': '已取消'
  };
  return statusMap[status] || status;
};

const getStatusClass = (status) => {
  const classMap = {
    '待处理': 'status-pending',
    '处理中': 'status-processing',
    '已完成': 'status-completed',
    '已取消': 'status-cancelled'
  };
  return classMap[status] || '';
};

const getPriorityLabel = (priority) => {
  const priorityMap = {
    '0': '高优先级',
    '1': '中优先级',
    '2': '低优先级'
  };
  return priorityMap[priority] || priority;
};

const getPriorityClass = (priority) => {
  const classMap = {
    '高优先级': 'priority-high',
    '中优先级': 'priority-medium',
    '低优先级': 'priority-low'
  };
  return classMap[priority] || '';
};

const getCategoryLabel = (category) => {
  const categoryMap = {
    '0': '水电维修',
    '1': '电器维修',
    '2': '空调维修',
    '3': '结构维修',
    '4': '清洁服务',
    '5': '其他'
  };
  return categoryMap[category] || category;
};

// 格式化日期为 yyyy-MM-dd HH:mm:ss
const formatDate = (dateStr) => {
  if (!dateStr) return '';
  const date = new Date(dateStr);
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, '0');
  const day = String(date.getDate()).padStart(2, '0');
  const hours = String(date.getHours()).padStart(2, '0');
  const minutes = String(date.getMinutes()).padStart(2, '0');
  const seconds = String(date.getSeconds()).padStart(2, '0');
  return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
};

// 格式化日期为 yyyy-MM-dd（用于日期选择器）
const formatDateOnly = (dateStr) => {
  if (!dateStr) return '';
  const date = new Date(dateStr);
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, '0');
  const day = String(date.getDate()).padStart(2, '0');
  return `${year}-${month}-${day}`;
};

// 将日期字符串转换为 yyyy-MM-dd HH:mm:ss 格式
const formatDateTimeForAPI = (dateStr) => {
  if (!dateStr) return '';
  // 如果只有日期，添加默认时间
  if (dateStr.length === 10) {
    return `${dateStr} 00:00:00`;
  }
  return dateStr;
};



// 处理搜索
const handleSearch = () => {
  currentPage.value = 1;
  loadWorkOrderList();
};

// 处理筛选
const handleFilter = (filterItem) => {
  filterValue.value = filterItem;
  currentPage.value = 1;
  loadWorkOrderList();
};

// 获取筛选状态对应的值
const getFilterStatusValue = (filterLabel) => {
  const statusMap = {
    '待处理': '0',
    '处理中': '1',
    '已完成': '2',
    '已取消': '3'
  };
  return statusMap[filterLabel];
};

// 获取用户列表
const loadUserOptions = async () => {
  try {
    loadingUsers.value = true;
    const response = await getAllUserListApi({});

    if (response.code === 200 && response.data) {
      userOptions.length = 0;
      response.data.forEach(user => {
        userOptions.push({
          value: user.userId,
          label: user.nickname || user.username,
          // 保存完整的用户信息用于自动填入
          userData: {
            userId: user.userId,
            username: user.username,
            nickname: user.nickname,
            email: user.email,
            phone: user.phone,
            sex: user.sex
          }
        });
      });
    } else {
      ElMessage.error('获取用户列表失败');
    }
  } catch (error) {
    console.error('获取用户列表失败:', error);
    ElMessage.error('获取用户列表失败，请稍后重试');
  } finally {
    loadingUsers.value = false;
  }
};

// 获取房产列表
const loadHouseOptions = async () => {
  try {
    loadingHouses.value = true;
    const response = await getAllHouseListApi({});

    if (response.code === 200 && response.data) {
      houseOptions.length = 0;
      response.data.forEach(house => {
        houseOptions.push({
          value: house.houseId || house.id,
          label: `${house.building} ${house.room}`
        });
      });
    } else {
      ElMessage.error('获取房产列表失败');
    }
  } catch (error) {
    console.error('获取房产列表失败:', error);
    ElMessage.error('获取房产列表失败，请稍后重试');
  } finally {
    loadingHouses.value = false;
  }
};

// 获取工单列表
const loadWorkOrderList = async () => {
  try {
    loadingWorkOrders.value = true;
    const response = await getRepairOrderListApi({
      pageNum: currentPage.value,
      pageSize: pageSize.value,
      searchKey: searchKeyword.value || undefined,
      status: filterValue.value === '全部状态' ? undefined : getFilterStatusValue(filterValue.value)
    });
    if (response.code === 200 && response.data) {
      workOrders.length = 0;
      response.data.forEach(workOrder => {
        workOrders.push({
          orderId: workOrder.orderId || workOrder.id,
          userId: workOrder.userId,
          title: workOrder.title,
          description: workOrder.description,
          category: getCategoryLabel(workOrder.category),
          status: getStatusLabel(workOrder.status),
          priority: getPriorityLabel(workOrder.priority),
          reporter: workOrder.reporter,
          reporterName: workOrder.reporterName || '未指定',
          houseId: workOrder.houseId,
          houseName: workOrder.houseName || '未指定',
          handlerId: workOrder.handlerId,
          handlerName: workOrder.handlerName || '未分配',
          cost: `¥${workOrder.cost}`,
          createTime: formatDate(workOrder.createTime),
          updateTime: formatDate(workOrder.updateTime)
        });
      });

      total.value = response.total || workOrders.length;
    } else {
      ElMessage.error('获取工单列表失败');
    }
  } catch (error) {
    console.error('获取工单列表失败:', error);
    ElMessage.error('获取工单列表失败，请稍后重试');
  } finally {
    loadingWorkOrders.value = false;
  }
};

// 锁定页面滚动
const lockBodyScroll = () => {
  const scrollY = window.scrollY;
  document.documentElement.classList.add('dialog-open');
  document.body.classList.add('dialog-open');
  document.body.style.overflow = 'hidden';
  document.body.style.position = 'fixed';
  document.body.style.top = `-${scrollY}px`;
  document.body.style.left = '0';
  document.body.style.right = '0';
  document.body.style.width = '100%';
  document.body.style.height = '100%';
  document.body.dataset.scrollY = scrollY.toString();
};

// 解锁页面滚动
const unlockBodyScroll = () => {
  const scrollY = parseInt(document.body.dataset.scrollY || '0');
  document.documentElement.classList.remove('dialog-open');
  document.body.classList.remove('dialog-open');
  document.body.style.overflow = '';
  document.body.style.position = '';
  document.body.style.top = '';
  document.body.style.left = '';
  document.body.style.right = '';
  document.body.style.width = '';
  document.body.style.height = '';
  delete document.body.dataset.scrollY;
  window.scrollTo(0, scrollY);
};

// 添加新工单
const addNewWorkOrder = async () => {
  addDialogVisible.value = true;
  lockBodyScroll();

  // 加载选项数据
  await Promise.all([loadUserOptions(), loadHouseOptions()]);

  // 重置表单
  if (addFormRef.value) {
    addFormRef.value.resetFields();
  }
  // 重置表单数据
  Object.assign(newWorkOrder, {
    reporter: '',
    houseId: '',
    title: '',
    description: '',
    category: '0',
    status: '0',
    priority: '1',
    handlerId: '',
    cost: '',
  });
};

// 提交新工单表单
const submitNewWorkOrder = async () => {
  if (!addFormRef.value) return;

  await addFormRef.value.validate(async (valid) => {
    if (valid) {
      try {
        isSubmitting.value = true;

        const response = await addRepairOrderApi({
          title: newWorkOrder.title,
          description: newWorkOrder.description,
          category: newWorkOrder.category,
          status: newWorkOrder.status,
          priority: newWorkOrder.priority,
          reporter: newWorkOrder.reporter ? parseInt(newWorkOrder.reporter) : 0,
          houseId: newWorkOrder.houseId ? parseInt(newWorkOrder.houseId) : 0,
          handlerId: newWorkOrder.handlerId ? parseInt(newWorkOrder.handlerId) : 0,
          cost: parseFloat(newWorkOrder.cost)
        });

        if (response.code === 200) {
          ElMessage.success('添加工单成功');
          addDialogVisible.value = false;
          unlockBodyScroll();
          await loadWorkOrderList();
        } else {
          ElMessage.error(response.msg || '添加工单失败');
        }
      } catch (error) {
        console.error('添加工单失败:', error);
        ElMessage.error('添加工单失败，请稍后重试');
      } finally {
        isSubmitting.value = false;
      }
    }
  });
};

// 取消添加
const cancelAdd = () => {
  addDialogVisible.value = false;
  unlockBodyScroll();
};

// 查看工单详情
const viewWorkOrderDetail = async (workOrder) => {
  try {
    // 确保用户选项和房产选项已加载
    if (userOptions.length === 0 || houseOptions.length === 0) {
      await Promise.all([loadUserOptions(), loadHouseOptions()]);
    }

    const response = await getRepairOrderByIdApi(workOrder.orderId || workOrder.id);
    if (response.code === 200 && response.data) {
      // 根据报修人ID查找姓名
      const reporterUser = userOptions.find(u => u.value === response.data.reporter);
      const reporterName = reporterUser ? reporterUser.label : '未指定';

      // 根据房产ID查找房产名称
      const house = houseOptions.find(h => h.value === response.data.houseId);
      const houseName = house ? house.label : '未指定';

      Object.assign(viewWorkOrderData, {
        ...response.data,
        reporterName: reporterName,
        houseName: houseName,
        statusLabel: getStatusLabel(response.data.status),
        priorityLabel: getPriorityLabel(response.data.priority),
        categoryLabel: getCategoryLabel(response.data.category),
        createTimeFormatted: formatDate(response.data.createTime),
        updateTimeFormatted: formatDate(response.data.updateTime)
      });
      viewDialogVisible.value = true;
      lockBodyScroll();
    } else {
      ElMessage.error('获取工单详情失败');
    }
  } catch (error) {
    console.error('获取工单详情失败:', error);
    ElMessage.error('获取工单详情失败，请稍后重试');
  }
};

// 关闭查看详情对话框
const closeViewDialog = () => {
  viewDialogVisible.value = false;
  unlockBodyScroll();
};

// 编辑工单
const editWorkOrder = async (workOrder) => {
  try {
    // 先加载选项数据
    await Promise.all([loadUserOptions(), loadHouseOptions()]);

    const response = await getRepairOrderByIdApi(workOrder.orderId || workOrder.id);
    if (response.code === 200 && response.data) {
      Object.assign(editWorkOrderData, {
        orderId: response.data.orderId || response.data.id,
        reporter: response.data.reporter || '',
        houseId: response.data.houseId || '',
        title: response.data.title,
        description: response.data.description,
        category: response.data.category,
        status: response.data.status,
        priority: response.data.priority,
        handlerId: response.data.handlerId || '',
        cost: response.data.cost,
      });

      editDialogVisible.value = true;
      lockBodyScroll();

      // 重置表单验证状态
      if (editFormRef.value) {
        editFormRef.value.clearValidate();
      }
    } else {
      ElMessage.error('获取工单信息失败');
    }
  } catch (error) {
    console.error('获取工单信息失败:', error);
    ElMessage.error('获取工单信息失败，请稍后重试');
  }
};

// 提交编辑工单表单
const submitEditWorkOrder = async () => {
  if (!editFormRef.value) return;

  await editFormRef.value.validate(async (valid) => {
    if (valid) {
      try {
        isEditSubmitting.value = true;

        const response = await editRepairOrderApi({
          orderId: parseInt(editWorkOrderData.orderId),
          title: editWorkOrderData.title,
          description: editWorkOrderData.description,
          category: editWorkOrderData.category,
          status: editWorkOrderData.status,
          priority: editWorkOrderData.priority,
          reporter: editWorkOrderData.reporter ? parseInt(editWorkOrderData.reporter) : 0,
          houseId: editWorkOrderData.houseId ? parseInt(editWorkOrderData.houseId) : 0,
          handlerId: editWorkOrderData.handlerId ? parseInt(editWorkOrderData.handlerId) : 0,
          cost: parseFloat(editWorkOrderData.cost),
        });

        if (response.code === 200) {
          ElMessage.success('编辑工单成功');
          editDialogVisible.value = false;
          unlockBodyScroll();
          await loadWorkOrderList();
        } else {
          ElMessage.error(response.msg || '编辑工单失败');
        }
      } catch (error) {
        console.error('编辑工单失败:', error);
        ElMessage.error('编辑工单失败，请稍后重试');
      } finally {
        isEditSubmitting.value = false;
      }
    }
  });
};

// 取消编辑
const cancelEdit = () => {
  editDialogVisible.value = false;
  unlockBodyScroll();
};

// 删除工单
const deleteWorkOrder = async (workOrder) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除工单 "${workOrder.title}" 吗？此操作不可撤销。`,
      '删除确认',
      {
        confirmButtonText: '确定删除',
        cancelButtonText: '取消',
        type: 'warning',
        confirmButtonClass: 'el-button--danger'
      }
    );

    const response = await deleteRepairOrderApi(workOrder.orderId || workOrder.id);
    if (response.code === 200) {
      ElMessage.success('删除工单成功');
      await loadWorkOrderList();
    } else {
      ElMessage.error(response.msg || '删除工单失败');
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除工单失败:', error);
      ElMessage.error('删除工单失败，请稍后重试');
    }
  }
};

// 处理页码变化
const handleCurrentChange = (val) => {
  currentPage.value = val;
  loadWorkOrderList();
};

// 监听对话框关闭事件
watch([addDialogVisible, editDialogVisible, viewDialogVisible], ([add, edit, view]) => {
  if (!add && !edit && !view) {
    // 移除滚动事件监听器
    window.removeEventListener('scroll', preventScroll);
    unlockBodyScroll();
  }
});

// 阻止滚动事件
const preventScroll = (e) => {
  e.preventDefault();
  e.stopPropagation();
  return false;
};

// 组件挂载时加载数据
onMounted(() => {
  loadWorkOrderList();
});
</script>

<template>
  <BackHeader activeMenu="workorders">
    <div class="workorders-container">
      <!-- 页面标题和添加按钮 -->
      <div class="page-header">
        <div class="title-section">
          <h1 class="page-title">工单管理</h1>
          <p class="page-subtitle">管理住户报修和维修服务请求</p>
        </div>

        <el-button type="primary" class="add-workorder-btn" @click="addNewWorkOrder">
          <el-icon><Plus /></el-icon>
          创建工单
        </el-button>
      </div>

      <!-- 添加工单对话框 -->
      <el-dialog
        v-model="addDialogVisible"
        title="创建新工单"
        width="800px"
        :close-on-click-modal="false"
        :modal="true"
        :lock-scroll="true"
        :center="true"
        :destroy-on-close="true"
        :top="'5vh'"
        class="add-workorder-dialog"
      >
        <div class="dialog-content">
          <el-form
            ref="addFormRef"
            :model="newWorkOrder"
            :rules="rules"
            label-width="100px"
            label-position="right"
            class="workorder-form"
          >
            <!-- 基本信息 -->
            <div class="form-section">
              <h3 class="section-title">
                <el-icon><User /></el-icon>
                基本信息
              </h3>

              <el-form-item label="报修人" prop="reporter">
                <el-select
                  v-model="newWorkOrder.reporter"
                  placeholder="请选择报修人"
                  style="width: 100%"
                  clearable
                  :loading="loadingUsers"
                  loading-text="加载用户列表中..."
                  no-data-text="暂无用户数据"
                >
                  <el-option
                    v-for="user in userOptions"
                    :key="user.value"
                    :label="user.label"
                    :value="user.value"
                  />
                </el-select>
              </el-form-item>

              <el-form-item label="房产" prop="houseId">
                <el-select
                  v-model="newWorkOrder.houseId"
                  placeholder="请选择房产"
                  style="width: 100%"
                  clearable
                  :loading="loadingHouses"
                  loading-text="加载房产列表中..."
                  no-data-text="暂无房产数据"
                >
                  <el-option
                    v-for="house in houseOptions"
                    :key="house.value"
                    :label="house.label"
                    :value="house.value"
                  />
                </el-select>
              </el-form-item>

              <el-form-item label="工单标题" prop="title">
                <el-input
                  v-model="newWorkOrder.title"
                  placeholder="请输入工单标题"
                />
              </el-form-item>

              <el-form-item label="问题描述" prop="description">
                <el-input
                  v-model="newWorkOrder.description"
                  type="textarea"
                  :rows="4"
                  placeholder="请详细描述问题"
                />
              </el-form-item>
            </div>

            <!-- 分类信息 -->
            <div class="form-section">
              <h3 class="section-title">
                <el-icon><Tools /></el-icon>
                分类信息
              </h3>

              <el-form-item label="问题类别" prop="category">
                <el-select
                  v-model="newWorkOrder.category"
                  placeholder="请选择问题类别"
                  style="width: 100%"
                >
                  <el-option label="水电维修" value="0" />
                  <el-option label="电器维修" value="1" />
                  <el-option label="空调维修" value="2" />
                  <el-option label="结构维修" value="3" />
                  <el-option label="清洁服务" value="4" />
                  <el-option label="其他" value="5" />
                </el-select>
              </el-form-item>

              <el-form-item label="优先级" prop="priority">
                <el-select
                  v-model="newWorkOrder.priority"
                  placeholder="请选择优先级"
                  style="width: 100%"
                >
                  <el-option label="高" value="0" />
                  <el-option label="中" value="1" />
                  <el-option label="低" value="2" />
                </el-select>
              </el-form-item>

              <el-form-item label="工单状态" prop="status">
                <el-select
                  v-model="newWorkOrder.status"
                  placeholder="请选择工单状态"
                  style="width: 100%"
                >
                  <el-option label="待处理" value="0" />
                  <el-option label="处理中" value="1" />
                  <el-option label="已完成" value="2" />
                  <el-option label="已取消" value="3" />
                </el-select>
              </el-form-item>
            </div>

            <!-- 人员信息 -->
            <div class="form-section">
              <h3 class="section-title">
                <el-icon><User /></el-icon>
                人员信息
              </h3>

              <el-form-item label="处理人" prop="handlerId">
                <el-select
                  v-model="newWorkOrder.handlerId"
                  placeholder="请选择处理人"
                  style="width: 100%"
                  clearable
                  :loading="loadingUsers"
                  loading-text="加载用户列表中..."
                  no-data-text="暂无用户数据"
                >
                  <el-option
                    v-for="user in userOptions"
                    :key="user.value"
                    :label="user.label"
                    :value="user.value"
                  />
                </el-select>
              </el-form-item>
            </div>

            <!-- 费用信息 -->
            <div class="form-section">
              <h3 class="section-title">
                <el-icon><Money /></el-icon>
                费用信息
              </h3>

              <el-form-item label="维修费用" prop="cost">
                <el-input
                  v-model="newWorkOrder.cost"
                  placeholder="请输入维修费用"
                >
                  <template #prepend>¥</template>
                </el-input>
              </el-form-item>
            </div>

          </el-form>
        </div>

        <template #footer>
          <div class="dialog-footer">
            <el-button @click="cancelAdd">取消</el-button>
            <el-button
              type="primary"
              @click="submitNewWorkOrder"
              :loading="isSubmitting"
            >
              {{ isSubmitting ? '创建中...' : '确认创建' }}
            </el-button>
          </div>
        </template>
      </el-dialog>

      <!-- 查看工单详情对话框 -->
      <el-dialog
        v-model="viewDialogVisible"
        title="工单详情"
        width="600px"
        :close-on-click-modal="false"
        :modal="true"
        :lock-scroll="true"
        :center="true"
        :destroy-on-close="true"
        class="view-workorder-dialog"
      >
        <div class="view-content">
          <div class="detail-section">
            <h3 class="section-title">
              <el-icon><Tools /></el-icon>
              基本信息
            </h3>
            <div class="detail-grid">
              <div class="detail-item">
                <span class="detail-label">工单标题：</span>
                <span class="detail-value">{{ viewWorkOrderData.title }}</span>
              </div>
              <div class="detail-item">
                <span class="detail-label">报修人：</span>
                <span class="detail-value">{{ viewWorkOrderData.reporterName }}</span>
              </div>
              <div class="detail-item">
                <span class="detail-label">房产：</span>
                <span class="detail-value">{{ viewWorkOrderData.houseName || '未指定' }}</span>
              </div>
              <div class="detail-item">
                <span class="detail-label">问题类别：</span>
                <span class="detail-value">{{ viewWorkOrderData.categoryLabel }}</span>
              </div>
              <div class="detail-item">
                <span class="detail-label">优先级：</span>
                <el-tag
                  :type="viewWorkOrderData.priorityLabel === '高优先级' ? 'danger' :
                      viewWorkOrderData.priorityLabel === '中优先级' ? 'warning' : 'info'"
                  size="small"
                >
                  {{ viewWorkOrderData.priorityLabel }}
                </el-tag>
              </div>
              <div class="detail-item">
                <span class="detail-label">工单状态：</span>
                <el-tag
                  :type="viewWorkOrderData.statusLabel === '已完成' ? 'success' :
                      viewWorkOrderData.statusLabel === '处理中' ? 'warning' :
                      viewWorkOrderData.statusLabel === '已取消' ? 'info' : 'danger'"
                  size="small"
                >
                  {{ viewWorkOrderData.statusLabel }}
                </el-tag>
              </div>
              <div class="detail-item">
                <span class="detail-label">维修费用：</span>
                <span class="detail-value cost-highlight">¥{{ viewWorkOrderData.cost }}</span>
              </div>
              <div class="detail-item">
                <span class="detail-label">创建时间：</span>
                <span class="detail-value">{{ viewWorkOrderData.createTimeFormatted }}</span>
              </div>
              <div class="detail-item">
                <span class="detail-label">更新时间：</span>
                <span class="detail-value">{{ viewWorkOrderData.updateTimeFormatted }}</span>
              </div>
              <div class="detail-item detail-full">
                <span class="detail-label">问题描述：</span>
                <span class="detail-value">{{ viewWorkOrderData.description }}</span>
              </div>
            </div>
          </div>
        </div>

        <template #footer>
          <div class="dialog-footer">
            <el-button @click="closeViewDialog">关闭</el-button>
          </div>
        </template>
      </el-dialog>

      <!-- 编辑工单对话框 -->
      <el-dialog
        v-model="editDialogVisible"
        title="编辑工单"
        width="800px"
        :close-on-click-modal="false"
        :modal="true"
        :lock-scroll="true"
        :center="true"
        :destroy-on-close="true"
        :top="'5vh'"
        class="edit-workorder-dialog"
      >
        <div class="dialog-content">
          <el-form
            ref="editFormRef"
            :model="editWorkOrderData"
            :rules="rules"
            label-width="100px"
            label-position="right"
            class="workorder-form"
          >
            <!-- 基本信息 -->
            <div class="form-section">
              <h3 class="section-title">
                <el-icon><User /></el-icon>
                基本信息
              </h3>

              <el-form-item label="报修人" prop="reporter">
                <el-select
                  v-model="editWorkOrderData.reporter"
                  placeholder="请选择报修人"
                  style="width: 100%"
                  clearable
                  :loading="loadingUsers"
                  loading-text="加载用户列表中..."
                  no-data-text="暂无用户数据"
                >
                  <el-option
                    v-for="user in userOptions"
                    :key="user.value"
                    :label="user.label"
                    :value="user.value"
                  />
                </el-select>
              </el-form-item>

              <el-form-item label="房产" prop="houseId">
                <el-select
                  v-model="editWorkOrderData.houseId"
                  placeholder="请选择房产"
                  style="width: 100%"
                  clearable
                  :loading="loadingHouses"
                  loading-text="加载房产列表中..."
                  no-data-text="暂无房产数据"
                >
                  <el-option
                    v-for="house in houseOptions"
                    :key="house.value"
                    :label="house.label"
                    :value="house.value"
                  />
                </el-select>
              </el-form-item>

              <el-form-item label="工单标题" prop="title">
                <el-input
                  v-model="editWorkOrderData.title"
                  placeholder="请输入工单标题"
                />
              </el-form-item>

              <el-form-item label="问题描述" prop="description">
                <el-input
                  v-model="editWorkOrderData.description"
                  type="textarea"
                  :rows="4"
                  placeholder="请详细描述问题"
                />
              </el-form-item>
            </div>

            <!-- 分类信息 -->
            <div class="form-section">
              <h3 class="section-title">
                <el-icon><Tools /></el-icon>
                分类信息
              </h3>

              <el-form-item label="问题类别" prop="category">
                <el-select
                  v-model="editWorkOrderData.category"
                  placeholder="请选择问题类别"
                  style="width: 100%"
                >
                  <el-option label="水电维修" value="0" />
                  <el-option label="电器维修" value="1" />
                  <el-option label="空调维修" value="2" />
                  <el-option label="结构维修" value="3" />
                  <el-option label="清洁服务" value="4" />
                  <el-option label="其他" value="5" />
                </el-select>
              </el-form-item>

              <el-form-item label="优先级" prop="priority">
                <el-select
                  v-model="editWorkOrderData.priority"
                  placeholder="请选择优先级"
                  style="width: 100%"
                >
                  <el-option label="高优先级" value="0" />
                  <el-option label="中优先级" value="1" />
                  <el-option label="低优先级" value="2" />
                </el-select>
              </el-form-item>

              <el-form-item label="工单状态" prop="status">
                <el-select
                  v-model="editWorkOrderData.status"
                  placeholder="请选择工单状态"
                  style="width: 100%"
                >
                  <el-option label="待处理" value="0" />
                  <el-option label="处理中" value="1" />
                  <el-option label="已完成" value="2" />
                  <el-option label="已取消" value="3" />
                </el-select>
              </el-form-item>
            </div>

            <!-- 人员信息 -->
            <div class="form-section">
              <h3 class="section-title">
                <el-icon><User /></el-icon>
                人员信息
              </h3>

              <el-form-item label="处理人" prop="handlerId">
                <el-select
                  v-model="editWorkOrderData.handlerId"
                  placeholder="请选择处理人"
                  style="width: 100%"
                  clearable
                  :loading="loadingUsers"
                  loading-text="加载用户列表中..."
                  no-data-text="暂无用户数据"
                >
                  <el-option
                    v-for="user in userOptions"
                    :key="user.value"
                    :label="user.label"
                    :value="user.value"
                  />
                </el-select>
              </el-form-item>
            </div>

            <!-- 费用信息 -->
            <div class="form-section">
              <h3 class="section-title">
                <el-icon><Money /></el-icon>
                费用信息
              </h3>

              <el-form-item label="维修费用" prop="cost">
                <el-input
                  v-model="editWorkOrderData.cost"
                  placeholder="请输入维修费用"
                >
                  <template #prepend>¥</template>
                </el-input>
              </el-form-item>
            </div>

          </el-form>
        </div>

        <template #footer>
          <div class="dialog-footer">
            <el-button @click="cancelEdit">取消</el-button>
            <el-button
              type="primary"
              @click="submitEditWorkOrder"
              :loading="isEditSubmitting"
            >
              {{ isEditSubmitting ? '保存中...' : '保存修改' }}
            </el-button>
          </div>
        </template>
      </el-dialog>

      <!-- 搜索和筛选 -->
      <div class="search-filter-bar">
        <div class="search-box">
          <el-input
            v-model="searchKeyword"
            placeholder="搜索工单标题、描述、报修人..."
            class="search-input"
            @keyup.enter="handleSearch"
          >
            <template #prefix>
              <el-icon><Search /></el-icon>
            </template>
          </el-input>
        </div>

        <div class="filter-box">
          <el-dropdown>
            <el-button class="filter-button">
              {{ filterValue }}
              <el-icon class="el-icon--right"><ArrowDown /></el-icon>
            </el-button>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item @click="handleFilter('全部状态')">全部状态</el-dropdown-item>
                <el-dropdown-item @click="handleFilter('待处理')">待处理</el-dropdown-item>
                <el-dropdown-item @click="handleFilter('处理中')">处理中</el-dropdown-item>
                <el-dropdown-item @click="handleFilter('已完成')">已完成</el-dropdown-item>
                <el-dropdown-item @click="handleFilter('已取消')">已取消</el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </div>
      </div>

      <!-- 工单网格 -->
      <div class="workorders-grid">
        <!-- 加载状态 -->
        <div v-if="loadingWorkOrders" class="loading-container">
          <el-icon class="loading-icon" :size="40">
            <Loading />
          </el-icon>
          <p class="loading-text">加载工单列表中...</p>
        </div>

        <!-- 空状态 -->
        <div v-else-if="workOrders.length === 0" class="empty-container">
          <el-icon class="empty-icon" :size="60">
            <Tools />
          </el-icon>
          <p class="empty-text">暂无工单数据</p>
          <el-button type="primary" @click="addNewWorkOrder">
            <el-icon><Plus /></el-icon>
            创建工单
          </el-button>
        </div>

        <!-- 工单卡片 -->
        <div v-else v-for="workOrder in workOrders" :key="workOrder.id" class="workorder-card">
          <div class="workorder-header">
            <div class="workorder-id">
              <el-icon><Setting /></el-icon>
              <span class="workorder-title">{{ workOrder.title }}</span>
            </div>
            <div class="status-tag" :class="getStatusClass(workOrder.status)">
              {{ workOrder.status }}
            </div>
          </div>

          <div class="workorder-content">
            <div class="info-row">
              <div class="info-item">
                <div class="info-label">报修人：</div>
                <div class="info-value">{{ workOrder.reporterName }}</div>
              </div>
              <div class="info-item">
                <div class="info-label">房产：</div>
                <div class="info-value">{{ workOrder.houseName }}</div>
              </div>
            </div>

            <div class="info-row">
              <div class="info-item">
                <div class="info-label">处理人：</div>
                <div class="info-value">{{ workOrder.handlerName }}</div>
              </div>
              <div class="info-item">
                <div class="info-label">优先级：</div>
                <div class="info-value">
                  <span class="priority-tag" :class="getPriorityClass(workOrder.priority)">
                    {{ workOrder.priority }}
                  </span>
                </div>
              </div>
            </div>

            <div class="info-row">
              <div class="info-item">
                <div class="info-label">类别：</div>
                <div class="info-value">{{ workOrder.category }}</div>
              </div>
              <div class="info-item">
                <div class="info-label">维修费用1：</div>
                <div class="info-value cost">{{ workOrder.cost }}</div>
              </div>
            </div>

            <div class="description-row">
              <div class="description">{{ workOrder.description }}</div>
            </div>

            <div class="workorder-footer">
              <div class="create-time">
                <el-icon><Calendar /></el-icon>
                创建时间：{{ workOrder.createTime }}
              </div>
              <div class="action-buttons">
                <el-button
                  type="info"
                  size="small"
                  @click="viewWorkOrderDetail(workOrder)"
                  title="查看详情"
                >
                  <el-icon><View /></el-icon>
                  查看
                </el-button>
                <el-button
                  type="primary"
                  size="small"
                  @click="editWorkOrder(workOrder)"
                  title="编辑工单"
                >
                  <el-icon><Edit /></el-icon>
                  编辑
                </el-button>
                <el-button
                  type="danger"
                  size="small"
                  @click="deleteWorkOrder(workOrder)"
                  title="删除工单"
                >
                  <el-icon><Delete /></el-icon>
                  删除
                </el-button>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 分页 -->
      <div class="pagination-container">
        <div class="total-info">共 {{ total }} 条记录</div>
        <el-pagination
          background
          layout="prev, pager, next"
          :total="total"
          :current-page="currentPage"
          @current-change="handleCurrentChange"
        />
      </div>
    </div>
  </BackHeader>
</template>

<style scoped>
.workorders-container {
  padding: 0 20px 20px;
  max-width: 1800px;
  margin: 0 auto;
  width: 100%;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
}

.page-title {
  font-size: 28px;
  font-weight: 600;
  margin: 0;
  color: #333;
}

.page-subtitle {
  font-size: 16px;
  color: #828385;
  margin: 10px 0 0 0;
}

.add-workorder-btn {
  display: flex;
  align-items: center;
  gap: 10px;
  height: 48px;
  font-size: 18px;
  padding: 0 20px;
}



.search-filter-bar {
  display: flex;
  margin-bottom: 30px;
  align-items: center;
}

.search-box {
  width: 50%;
  margin-right: 20px;
}

.search-input {
  width: 100%;
}

.search-input :deep(.el-input__inner) {
  height: 52px;
  font-size: 18px;
  padding: 0 20px;
}

.filter-button {
  display: flex;
  align-items: center;
  height: 52px;
  font-size: 18px;
  padding: 0 20px;
}

/* 工单网格样式 */
.workorders-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 24px;
  margin-bottom: 32px;
}

@media (max-width: 1200px) {
  .workorders-grid {
    grid-template-columns: 1fr;
  }
}

.workorder-card {
  background-color: #fff;
  border-radius: 12px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
  overflow: hidden;
  position: relative;
  display: flex;
  flex-direction: column;
}

.workorder-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 24px;
  border-bottom: 1px solid #f0f0f0;
}

.workorder-id {
  display: flex;
  align-items: center;
}

.id-badge {
  background-color: #f5f7fa;
  color: #909399;
  padding: 6px 12px;
  border-radius: 6px;
  font-size: 14px;
  margin-right: 16px;
}

.workorder-title {
  padding-left: 10px;
  font-weight: 500;
  font-size: 18px;
  margin: 0;
}

.workorder-content {
  padding: 20px 24px;
  flex: 1;
  display: flex;
  flex-direction: column;
}

.info-row {
  display: flex;
  margin-bottom: 16px;
}

.info-item {
  display: flex;
  align-items: flex-start;
  flex: 1;
}

.info-label {
  color: #909399;
  font-size: 15px;
  min-width: 80px;
  text-align: right;
  margin-right: 12px;
}

.info-value {
  color: #606266;
  font-size: 15px;
  flex: 1;
}

.priority-tag {
  display: inline-block;
  padding: 2px 8px;
  border-radius: 4px;
  font-size: 12px;
}

.priority-high {
  background-color: #fef0f0;
  color: #f56c6c;
}

.priority-medium {
  background-color: #fdf6ec;
  color: #e6a23c;
}

.priority-low {
  background-color: #f0f9ff;
  color: #1890ff;
}

.cost {
  font-weight: 500;
}

.description-row {
  margin-bottom: 16px;
}

.description {
  white-space: normal;
  word-break: break-all;
  line-height: 1.5;
  color: #606266;
  font-size: 14px;
}

.workorder-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: auto;
  padding-top: 16px;
  border-top: 1px dashed #ebeef5;
}

.create-time {
  color: #909399;
  font-size: 12px;
  display: flex;
  align-items: center;
}

.create-time .el-icon {
  margin-right: 4px;
  font-size: 12px;
}

.action-buttons {
  display: flex;
  gap: 8px;
}

.status-tag {
  display: inline-block;
  padding: 6px 12px;
  border-radius: 4px;
  font-size: 15px;
  font-weight: 500;
}

.status-pending {
  background-color: #e6f7ff;
  color: #1890ff;
}

.status-processing {
  background-color: #fff7e6;
  color: #fa8c16;
}

.status-completed {
  background-color: #f6ffed;
  color: #52c41a;
}

.status-cancelled {
  background-color: #f5f5f5;
  color: #909399;
}

.pagination-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 30px;
}

.total-info {
  font-size: 18px;
  color: #909399;
}

/* 加载和空状态样式 */
.loading-container, .empty-container {
  grid-column: 1 / -1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  text-align: center;
  background-color: #fff;
  border-radius: 12px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
}

.loading-icon, .empty-icon {
  margin-bottom: 16px;
  color: #909399;
}

.loading-text, .empty-text {
  font-size: 16px;
  color: #909399;
  margin: 0 0 20px 0;
}

.loading-icon {
  animation: rotate 2s linear infinite;
}

@keyframes rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* 对话框样式 */
.add-workorder-dialog :deep(.el-dialog),
.edit-workorder-dialog :deep(.el-dialog) {
  margin-top: 0 !important;
  margin-bottom: 0 !important;
  max-height: 90vh !important;
}

.add-workorder-dialog :deep(.el-dialog__body),
.edit-workorder-dialog :deep(.el-dialog__body) {
  max-height: calc(90vh - 120px) !important;
  overflow-y: auto !important;
}

.dialog-content {
  width: 100%;
}

.workorder-form {
  width: 100%;
}

.form-section {
  margin-bottom: 20px;
  padding: 16px;
  background-color: #fafafa;
  border-radius: 6px;
  border: 1px solid #f0f0f0;
}

.form-section:last-child {
  margin-bottom: 0;
}

.section-title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 16px;
  font-weight: 600;
  color: #333;
  margin: 0 0 16px 0;
  padding-bottom: 8px;
  border-bottom: 2px solid #e6f7ff;
}

.section-title .el-icon {
  font-size: 18px;
  color: #1890ff;
}

.workorder-form :deep(.el-form-item) {
  margin-bottom: 16px;
}

.workorder-form :deep(.el-form-item__label) {
  font-size: 16px;
  font-weight: 500;
  color: #333;
  line-height: 1.4;
}

.workorder-form :deep(.el-input__wrapper) {
  border-radius: 6px;
  box-shadow: 0 0 0 1px #d9d9d9;
  transition: all 0.2s;
}

.workorder-form :deep(.el-input__wrapper:hover) {
  box-shadow: 0 0 0 1px #40a9ff;
}

.workorder-form :deep(.el-input__wrapper.is-focus) {
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
}

.workorder-form :deep(.el-input__inner) {
  font-size: 16px;
  color: #333;
}

.workorder-form :deep(.el-input-group__prepend),
.workorder-form :deep(.el-input-group__append) {
  background-color: #fafafa;
  border-color: #d9d9d9;
  color: #666;
  font-weight: 500;
  font-size: 16px;
}

.workorder-form :deep(.el-select .el-input__wrapper) {
  border-radius: 6px;
}

.workorder-form :deep(.el-textarea__inner) {
  border-radius: 6px;
  border-color: #d9d9d9;
  font-size: 16px;
  color: #333;
  resize: vertical;
}

.workorder-form :deep(.el-textarea__inner:hover) {
  border-color: #40a9ff;
}

.workorder-form :deep(.el-textarea__inner:focus) {
  border-color: #1890ff;
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  padding: 16px 24px 24px;
  border-top: 1px solid #f0f0f0;
  margin: 0 -24px -24px;
  background-color: #fff;
}

.dialog-footer .el-button {
  min-width: 100px;
  height: 40px;
  font-size: 16px;
  border-radius: 6px;
}

/* 查看详情对话框样式 */
.view-workorder-dialog :deep(.el-dialog) {
  border-radius: 12px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
}

.view-content {
  width: 100%;
}

.detail-section {
  margin-bottom: 20px;
}

.detail-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 16px 24px;
  margin-top: 16px;
}

.detail-item {
  display: flex;
  align-items: center;
  padding: 12px 0;
  border-bottom: 1px solid #f5f5f5;
}

.detail-full {
  grid-column: 1 / -1;
}

.detail-label {
  font-weight: 500;
  color: #666;
  min-width: 80px;
  margin-right: 12px;
}

.detail-value {
  color: #333;
  font-weight: 400;
}

.cost-highlight {
  color: #409EFF;
  font-weight: 600;
  font-size: 16px;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .search-filter-bar {
    flex-direction: column;
    align-items: stretch;
  }

  .search-box {
    width: 100%;
    margin-right: 0;
    margin-bottom: 12px;
  }
}

/* 全局阻止滚动 */
html:has(.add-workorder-dialog),
html.dialog-open {
  overflow: hidden !important;
  height: 100% !important;
}

body.dialog-open {
  overflow: hidden !important;
  position: fixed !important;
  top: 0 !important;
  left: 0 !important;
  right: 0 !important;
  bottom: 0 !important;
  width: 100% !important;
  height: 100% !important;
}
</style>