<script setup>
import { ref, reactive } from 'vue';
import { Search, Plus, Edit, Delete, View, Tools, Setting } from '@element-plus/icons-vue';
import BackHeader from '@/components/BackHeader.vue';

// 搜索关键词
const searchKeyword = ref('');

// 工单状态统计
const statusStats = reactive([
  { name: '待处理', count: 4, icon: 'el-icon-warning', color: '#409EFF' },
  { name: '处理中', count: 2, icon: 'el-icon-loading', color: '#E6A23C' },
  { name: '待验收', count: 1, icon: 'el-icon-time', color: '#F56C6C' },
  { name: '已完成', count: 1, icon: 'el-icon-success', color: '#67C23A' }
]);

// 工单数据
const workOrders = reactive([
  {
    id: 'WO001',
    title: '厨房水龙头漏水',
    status: '待处理',
    statusClass: 'status-pending',
    statusColor: 'orange',
    location: 'A栋1201',
    reporter: {
      name: '张三',
      phone: '138-0001-0001'
    },
    priority: '中优先级',
    priorityClass: 'medium-priority',
    cost: '¥150',
    description: '厨房水龙头一直在滴水，已经持续两天了，需要尽快处理。',
    createTime: '2024-01-15 09:30'
  },
  {
    id: 'WO002',
    title: '电梯故障',
    status: '处理中',
    statusClass: 'status-processing',
    statusColor: 'red',
    location: 'B栋电梯',
    reporter: {
      name: '物业部门',
      phone: ''
    },
    priority: '高优先级',
    priorityClass: 'high-priority',
    cost: '¥800',
    description: '2号电梯按钮失灵，按了没有反应，需要维修人员处理。',
    createTime: '2024-01-15 14:20'
  },
  {
    id: 'WO003',
    title: '门锁无法开启',
    status: '已完成',
    statusClass: 'status-completed',
    statusColor: 'green',
    location: 'C栋1505',
    reporter: {
      name: '王五',
      phone: '138-0003-0003'
    },
    priority: '中优先级',
    priorityClass: 'medium-priority',
    cost: '¥500',
    description: '房间大门无法正常开门，钥匙也无法使用。',
    createTime: '2024-01-14 14:45'
  },
  {
    id: 'WO004',
    title: '空调制冷效果差',
    status: '待处理',
    statusClass: 'status-pending',
    statusColor: 'green',
    location: 'A栋605',
    reporter: {
      name: '赵六',
      phone: '138-0004-0004'
    },
    priority: '中优先级',
    priorityClass: 'medium-priority',
    cost: '¥200',
    description: '家庭空调制冷效果很差，可能需要加制冷剂。',
    createTime: '2024-01-13 11:00'
  }
]);

// 处理搜索
const handleSearch = () => {
  console.log('搜索关键词:', searchKeyword.value);
  // 实际应用中这里应该调用API进行搜索
};

// 添加新工单
const addNewWorkOrder = () => {
  console.log('添加新工单');
  // 实际应用中这里应该打开添加工单的表单或对话框
};

// 查看工单详情
const viewWorkOrderDetail = (workOrder) => {
  console.log('查看工单详情:', workOrder);
  // 实际应用中这里应该打开工单详情页面或对话框
};

// 筛选条件
const filterValue = ref('全部状态');

// 当前页码
const currentPage = ref(1);
// 总条数
const total = ref(4);

// 处理页码变化
const handleCurrentChange = (val) => {
  currentPage.value = val;
  // 实际应用中这里应该重新加载数据
};
</script>

<template>
  <BackHeader activeMenu="workorders">
    <div class="workorders-container">
      <!-- 页面标题和添加按钮 -->
      <div class="page-header">
        <div class="title-section">
          <h1 class="page-title">维修工单</h1>
          <p class="page-subtitle">管理住户报修和维修服务请求</p>
        </div>

        <el-button type="primary" class="add-workorder-btn" @click="addNewWorkOrder">
          <el-icon><Plus /></el-icon>
          创建工单
        </el-button>
      </div>

      <!-- 状态统计卡片 -->
      <div class="status-cards">
        <div v-for="(stat, index) in statusStats" :key="index" class="status-card" :class="`status-card-${index}`">
          <div class="status-icon" :style="{ backgroundColor: stat.color + '20' }">
            <el-icon v-if="index === 0"><Tools /></el-icon>
            <el-icon v-else-if="index === 1"><Setting /></el-icon>
            <el-icon v-else-if="index === 2"><View /></el-icon>
            <el-icon v-else><el-icon-check /></el-icon>
          </div>
          <div class="status-info">
            <div class="status-count">{{ stat.count }}</div>
            <div class="status-name">{{ stat.name }}</div>
          </div>
        </div>
      </div>

      <!-- 搜索和筛选 -->
      <div class="search-filter-bar">
        <div class="search-box">
          <el-input
              v-model="searchKeyword"
              placeholder="搜索工单号、描述、报修人..."
              class="search-input"
              @keyup.enter="handleSearch"
          >
            <template #prefix>
              <el-icon><Search /></el-icon>
            </template>
          </el-input>
        </div>

        <div class="filter-box">
          <el-dropdown>
            <el-button class="filter-button">
              {{ filterValue }}
              <el-icon class="el-icon--right"><arrow-down /></el-icon>
            </el-button>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item>全部状态</el-dropdown-item>
                <el-dropdown-item>待处理</el-dropdown-item>
                <el-dropdown-item>处理中</el-dropdown-item>
                <el-dropdown-item>待验收</el-dropdown-item>
                <el-dropdown-item>已完成</el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </div>
      </div>

      <!-- 工单网格 -->
      <div class="workorders-grid">
        <div v-for="workOrder in workOrders" :key="workOrder.id" class="workorder-card">
          <div class="workorder-header">
            <div class="workorder-id">
              <span class="id-badge">{{ workOrder.id }}</span>
              <span class="workorder-title">{{ workOrder.title }}</span>
            </div>
            <el-button
                type="primary"
                text
                @click="viewWorkOrderDetail(workOrder)"
            >
              <el-icon><View /></el-icon>
            </el-button>
          </div>

          <div class="workorder-content">
            <div class="info-row">
              <div class="info-item">
                <div class="info-label">地点：</div>
                <div class="info-value">{{ workOrder.location }}</div>
              </div>
              <div class="info-item">
                <div class="info-label">房号：</div>
                <div class="info-value">{{ workOrder.location }}</div>
              </div>
            </div>

            <div class="info-row">
              <div class="info-item">
                <div class="info-label">报修人：</div>
                <div class="info-value">
                  <span v-if="workOrder.reporter.name !== '物业部门'">{{ workOrder.reporter.name }}</span>
                  <span v-else class="department-tag">{{ workOrder.reporter.name }}</span>
                  <span v-if="workOrder.reporter.phone" class="reporter-phone">
                    <el-icon><el-icon-phone /></el-icon>
                    {{ workOrder.reporter.phone }}
                  </span>
                </div>
              </div>
              <div class="info-item">
                <div class="info-label">优先级：</div>
                <div class="info-value">
                  <span class="priority-tag" :class="workOrder.priorityClass">
                    {{ workOrder.priority }}
                  </span>
                </div>
              </div>
            </div>

            <div class="info-row">
              <div class="info-item">
                <div class="info-label">维修费用：</div>
                <div class="info-value cost">{{ workOrder.cost }}</div>
              </div>
            </div>

            <div class="description-row">
              <div class="description">{{ workOrder.description }}</div>
            </div>

            <div class="workorder-footer">
              <div class="create-time">
                <el-icon><el-icon-time /></el-icon>
                创建时间：{{ workOrder.createTime }}
              </div>
              <div class="action-buttons">
                <el-button type="primary" size="small" link>
                  查看详情
                </el-button>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 分页 -->
      <div class="pagination-container">
        <div class="total-info">共 {{ total }} 条记录</div>
        <el-pagination
            background
            layout="prev, pager, next"
            :total="total"
            :current-page="currentPage"
            @current-change="handleCurrentChange"
        />
      </div>
    </div>
  </BackHeader>
</template>

<style scoped>
.workorders-container {
  padding: 0 24px 24px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 32px;
}

.page-title {
  font-size: 28px;
  font-weight: 600;
  margin: 0;
  color: #333;
}

.page-subtitle {
  font-size: 16px;
  color: #909399;
  margin: 10px 0 0 0;
}

.add-workorder-btn {
  display: flex;
  align-items: center;
  gap: 10px;
  height: 48px;
  font-size: 16px;
  padding: 0 20px;
}

.status-cards {
  display: flex;
  gap: 24px;
  margin-bottom: 32px;
}

.status-card {
  flex: 1;
  background-color: #fff;
  border-radius: 12px;
  padding: 28px;
  display: flex;
  align-items: center;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
}

.status-icon {
  width: 64px;
  height: 64px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 20px;
}

.status-icon .el-icon {
  font-size: 28px;
  color: #409EFF;
}

.status-card-0 .status-icon .el-icon {
  color: #409EFF;
}

.status-card-1 .status-icon .el-icon {
  color: #E6A23C;
}

.status-card-2 .status-icon .el-icon {
  color: #F56C6C;
}

.status-card-3 .status-icon .el-icon {
  color: #67C23A;
}

.status-info {
  display: flex;
  flex-direction: column;
}

.status-count {
  font-size: 32px;
  font-weight: 600;
  color: #333;
  margin-bottom: 8px;
}

.status-name {
  font-size: 16px;
  color: #909399;
}

.search-filter-bar {
  display: flex;
  margin-bottom: 32px;
}

.search-box {
  flex: 1;
  margin-right: 20px;
}

.search-input {
  width: 100%;
}

.search-input :deep(.el-input__inner) {
  height: 52px;
  font-size: 16px;
  padding: 0 20px;
}

.filter-button {
  display: flex;
  align-items: center;
  gap: 10px;
  height: 52px;
  font-size: 16px;
  padding: 0 20px;
}

.workorders-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 24px;
  margin-bottom: 32px;
}

@media (max-width: 1200px) {
  .workorders-grid {
    grid-template-columns: 1fr;
  }
}

.workorder-card {
  background-color: #fff;
  border-radius: 12px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
  overflow: hidden;
  position: relative;
  display: flex;
  flex-direction: column;
}

.workorder-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 24px;
  border-bottom: 1px solid #f0f0f0;
}

.workorder-id {
  display: flex;
  align-items: center;
}

.id-badge {
  background-color: #f5f7fa;
  color: #909399;
  padding: 6px 12px;
  border-radius: 6px;
  font-size: 14px;
  margin-right: 16px;
}

.workorder-title {
  font-weight: 500;
  font-size: 18px;
}

.workorder-content {
  padding: 20px 24px;
  flex: 1;
  display: flex;
  flex-direction: column;
}

.info-row {
  display: flex;
  margin-bottom: 16px;
}

.info-item {
  display: flex;
  align-items: flex-start;
  flex: 1;
}

.info-label {
  color: #909399;
  font-size: 15px;
  min-width: 80px;
  text-align: right;
  margin-right: 12px;
}

.info-value {
  color: #606266;
  font-size: 15px;
  flex: 1;
}

.reporter-phone {
  color: #409EFF;
  font-size: 12px;
  margin-left: 8px;
  display: flex;
  align-items: center;
}

.reporter-phone .el-icon {
  margin-right: 4px;
  font-size: 12px;
}

.department-tag {
  color: #409EFF;
  font-weight: 500;
}

.priority-tag {
  display: inline-block;
  padding: 2px 8px;
  border-radius: 4px;
  font-size: 12px;
}

.high-priority {
  background-color: #fef0f0;
  color: #f56c6c;
}

.medium-priority {
  background-color: #fdf6ec;
  color: #e6a23c;
}

.cost {
  font-weight: 500;
}

.description-row {
  margin-bottom: 16px;
}

.description {
  white-space: normal;
  word-break: break-all;
  line-height: 1.5;
  color: #606266;
  font-size: 14px;
}

.workorder-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: auto;
  padding-top: 16px;
  border-top: 1px dashed #ebeef5;
}

.create-time {
  color: #909399;
  font-size: 12px;
  display: flex;
  align-items: center;
}

.create-time .el-icon {
  margin-right: 4px;
  font-size: 12px;
}

.action-buttons {
  display: flex;
  gap: 8px;
}

.status-tag {
  display: inline-block;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
}

.status-pending {
  background-color: #e6f7ff;
  color: #1890ff;
}

.status-processing {
  background-color: #fff7e6;
  color: #fa8c16;
}

.status-reviewing {
  background-color: #fff2f0;
  color: #ff4d4f;
}

.status-completed {
  background-color: #f6ffed;
  color: #52c41a;
}

.pagination-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 20px;
}

.total-info {
  font-size: 14px;
  color: #909399;
}
</style>